{"cells": [{"cell_type": "code", "execution_count": 1, "id": "dc4ede85", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 5, "id": "b210238a", "metadata": {}, "outputs": [{"data": {"text/plain": ["7011"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["base_dir = r'D:\\code\\操作教程2\\Data\\visual_all'\n", "len(os.listdir(base_dir))"]}, {"cell_type": "code", "execution_count": 8, "id": "e2e4e3b0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总文件数: 7011\n", "每份文件数: 1169\n", "分割 1: 复制 1169 个文件到 D:\\code\\操作教程2\\Data\\split_file\\split_1\n", "分割 2: 复制 1169 个文件到 D:\\code\\操作教程2\\Data\\split_file\\split_2\n", "分割 3: 复制 1169 个文件到 D:\\code\\操作教程2\\Data\\split_file\\split_3\n", "分割 4: 复制 1169 个文件到 D:\\code\\操作教程2\\Data\\split_file\\split_4\n", "分割 5: 复制 1169 个文件到 D:\\code\\操作教程2\\Data\\split_file\\split_5\n", "分割 6: 复制 1166 个文件到 D:\\code\\操作教程2\\Data\\split_file\\split_6\n", "文件分割完成！\n"]}], "source": ["import os\n", "import shutil\n", "import math\n", "\n", "# 定义源目录和目标目录\n", "base_dir = r'D:\\code\\操作教程2\\Data\\visual_all'\n", "target_base_dir = r'D:\\code\\操作教程2\\Data\\split_file'\n", "\n", "# 获取所有文件并按名称排序\n", "all_files = sorted(os.listdir(base_dir))\n", "total_files = len(all_files)\n", "print(f\"总文件数: {total_files}\")\n", "\n", "# 计算每份的文件数量\n", "num_splits = 6\n", "files_per_split = math.ceil(total_files / num_splits)\n", "print(f\"每份文件数: {files_per_split}\")\n", "\n", "# 确保目标目录存在\n", "os.makedirs(target_base_dir, exist_ok=True)\n", "\n", "# 分割文件并复制到目标目录\n", "for i in range(num_splits):\n", "    # 创建子目录\n", "    split_dir = os.path.join(target_base_dir, f\"split_{i+1}\")\n", "    os.makedirs(split_dir, exist_ok=True)\n", "    \n", "    # 计算当前分割的起始和结束索引\n", "    start_idx = i * files_per_split\n", "    end_idx = min((i + 1) * files_per_split, total_files)\n", "    \n", "    # 获取当前分割的文件列表\n", "    split_files = all_files[start_idx:end_idx]\n", "    \n", "    print(f\"分割 {i+1}: 复制 {len(split_files)} 个文件到 {split_dir}\")\n", "    \n", "    # 复制文件\n", "    for file_name in split_files:\n", "        src_path = os.path.join(base_dir, file_name)\n", "        dst_path = os.path.join(split_dir, file_name)\n", "        shutil.copy2(src_path, dst_path)\n", "    \n", "    # # 创建一个文本文件，记录该分割包含的文件数量和文件范围\n", "    # with open(os.path.join(split_dir, \"_file_info.txt\"), \"w\") as f:\n", "    #     f.write(f\"本文件夹包含 {len(split_files)} 个文件\\n\")\n", "    #     f.write(f\"总文件数: {total_files}\\n\")\n", "    #     f.write(f\"分割编号: {i+1}/{num_splits}\\n\")\n", "    #     f.write(f\"文件范围: {split_files[0]} 到 {split_files[-1]}\\n\")\n", "\n", "print(\"文件分割完成！\")"]}], "metadata": {"kernelspec": {"display_name": "deeplearn", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}
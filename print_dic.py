# encoding: utf-8

import os

def print_directory_tree(startpath, indent='    ', exclude_dirs=None, exclude_files=None, 
                        max_files_to_show=None, max_dirs_to_show=None):
    """
    打印目录树结构。

    参数:
    startpath (str): 要打印的起始目录路径。
    indent (str): 用于缩进的字符串，默认为四个空格。
    exclude_dirs (list): 要排除的目录名称列表。
    exclude_files (list): 要排除的文件名称列表。
    max_files_to_show (int, optional): 每个目录下显示的最大文件数量。如果为 None，则显示所有文件。
    max_dirs_to_show (int, optional): 每个目录下显示的最大子目录数量。如果为 None，则显示所有子目录。
    """
    if exclude_dirs is None:
        exclude_dirs = []
    if exclude_files is None:
        exclude_files = []

    # 将常见的需要排除的目录加入默认排除列表
    common_exclude_dirs = ['.git', '__pycache__', '.vscode', '.idea', 'node_modules', 'venv']
    exclude_dirs.extend([d for d in common_exclude_dirs if d not in exclude_dirs])

    def process_directory(path, level=0):
        # 获取当前目录下的所有内容
        try:
            items = os.listdir(path)
        except PermissionError:
            return

        # 分离文件和目录
        dirs = []
        files = []
        for item in items:
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path) and item not in exclude_dirs:
                dirs.append(item)
            elif os.path.isfile(item_path) and item not in exclude_files:
                files.append(item)

        # 打印当前目录名
        dir_indent = indent * level
        print(f"{dir_indent}{os.path.basename(path)}/")

        # 打印文件
        file_indent = indent * (level + 1)
        files_to_print = files
        if max_files_to_show is not None and len(files) > max_files_to_show:
            files_to_print = files[:max_files_to_show]
            print(*(f"{file_indent}{f}" for f in files_to_print), sep='\n')
            print(f"{file_indent}... (+{len(files) - max_files_to_show} more files)")
        else:
            if files_to_print:  # 只有当有文件时才打印
                print(*(f"{file_indent}{f}" for f in files_to_print), sep='\n')

        # 处理子目录
        dirs_to_process = sorted(dirs)
        if max_dirs_to_show is not None and len(dirs) > max_dirs_to_show:
            dirs_to_process = dirs_to_process[:max_dirs_to_show]
            # 递归处理限制数量的子目录
            for d in dirs_to_process:
                process_directory(os.path.join(path, d), level + 1)
            # 显示有多少目录未显示
            dir_indent = indent * (level + 1)
            print(f"{dir_indent}... (+{len(dirs) - max_dirs_to_show} more directories)")
        else:
            # 递归处理所有子目录
            for d in dirs_to_process:
                process_directory(os.path.join(path, d), level + 1)
            
    # 开始处理根目录
    process_directory(startpath)

if __name__ == "__main__":
    project_path = r'D:\code\操作教程2\Data'
    # 设置每个目录最多显示2个文件和3个子目录
    
    print(f"数据目录结构 ({project_path}):")
    print()
    print_directory_tree(project_path, max_files_to_show=2, max_dirs_to_show=4)

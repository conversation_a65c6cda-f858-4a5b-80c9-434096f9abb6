#!/usr/bin/env python

from __future__ import print_function

import glob
import os
import os.path as osp
import sys

import imgviz
import numpy as np

import labelme


def convert_labelme_to_voc(
    input_dir,
    output_dir,
    labels,
    noobject=False,
    nonpy=False,
    noviz=False,
    debug=False,
    force_overwrite=False  # 新增参数，控制是否强制覆盖已存在的输出目录
):
    """
    将labelme标注的数据转换为VOC格式

    参数:
        input_dir: 输入的labelme标注目录
        output_dir: 输出的VOC格式数据集目录
        labels: 标签文件路径或逗号分隔的文本
        noobject: 是否不生成对象标签
        nonpy: 是否不生成.npy文件
        noviz: 是否禁用可视化
        debug: 是否开启调试模式
        force_overwrite: 是否强制覆盖已存在的输出目录
    """
    # 检查输出目录是否已存在
    if osp.exists(output_dir):
        if not force_overwrite:
            print("输出目录已存在:", output_dir)
            print("如果要覆盖已存在的目录，请设置 force_overwrite=True")
            return False
        else:
            print(f"警告：正在覆盖已存在的输出目录 {output_dir}")
            # 不删除目录，只清空内容，这样更安全
            # 我们会在后面创建必要的子目录

    # 创建输出目录结构
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(osp.join(output_dir, "JPEGImages"), exist_ok=True)
    os.makedirs(osp.join(output_dir, "SegmentationClass"), exist_ok=True)
    os.makedirs(osp.join(output_dir, "SegmentationClassColor"), exist_ok=True)  # 新增彩色掩码目录

    if not nonpy:
        os.makedirs(osp.join(output_dir, "SegmentationClassNpy"), exist_ok=True)
    if not noviz:
        os.makedirs(osp.join(output_dir, "SegmentationClassVisualization"), exist_ok=True)
        os.makedirs(osp.join(output_dir, "SegmentationClassVisualization2"), exist_ok=True)  # 新增边缘线可视化目录
    if not noobject:
        os.makedirs(osp.join(output_dir, "SegmentationObject"), exist_ok=True)
        if not nonpy:
            os.makedirs(osp.join(output_dir, "SegmentationObjectNpy"), exist_ok=True)
        if not noviz:
            os.makedirs(osp.join(output_dir, "SegmentationObjectVisualization"), exist_ok=True)
    print("创建数据集:", output_dir)

    if osp.exists(labels):
        with open(labels) as f:
            labels_list = [label.strip() for label in f if label.strip()]
        print(f"从文件加载的标签: {labels_list}")
    else:
        labels_list = [label.strip() for label in labels.split(",")]
        print(f"从字符串加载的标签: {labels_list}")

    class_names = []
    class_name_to_id = {}
    for i, label in enumerate(labels_list):
        class_id = i - 1  # starts with -1
        class_name = label.strip()
        class_name_to_id[class_name] = class_id
        if class_id == -1:
            assert class_name == "__ignore__", f"第一个标签应该是'__ignore__'，但得到的是'{class_name}'"
            continue
        elif class_id == 0:
            assert class_name == "_background_", f"第二个标签应该是'_background_'，但得到的是'{class_name}'"
        class_names.append(class_name)
    class_names = tuple(class_names)
    print("class_names:", class_names)
    print("class_name_to_id:", class_name_to_id)
    out_class_names_file = osp.join(output_dir, "class_names.txt")
    with open(out_class_names_file, "w") as f:
        f.writelines("\n".join(class_names))
    print("保存类名:", out_class_names_file)

    # 定义类别颜色映射 (R, G, B)
    class_colors = {
        "_background_": (0, 0, 0),  # 背景为黑色
        "1": (0, 255, 0),          # 类别1为绿色
        "2": (255, 0, 0)           # 类别2为红色
    }

    for filename in sorted(glob.glob(osp.join(input_dir, "*.json"))):
        print("从以下文件生成数据集:", filename)

        label_file = labelme.LabelFile(filename=filename)

        # 打印标注中的类别信息用于调试
        if debug:
            shapes = label_file.shapes
            label_names = [shape["label"] for shape in shapes]
            print(f"文件 {filename} 中的标签: {label_names}")
            print(f"是否包含在class_name_to_id中: {[name in class_name_to_id for name in label_names]}")

        # 修改标注处理顺序 - 按照类别ID排序
        if debug:
            print("原始标注顺序:", [shape["label"] for shape in label_file.shapes])

        # 确保标注按照类别ID从小到大排序，这样较小的ID会被后处理，从而不会被覆盖
        label_file.shapes.sort(key=lambda shape: class_name_to_id.get(shape["label"], 999), reverse=True)

        if debug:
            print("排序后标注顺序:", [shape["label"] for shape in label_file.shapes])

        base = osp.splitext(osp.basename(filename))[0]
        out_img_file = osp.join(output_dir, "JPEGImages", base + ".jpg")
        out_clsp_file = osp.join(output_dir, "SegmentationClass", base + ".png")
        if not nonpy:
            out_cls_file = osp.join(
                output_dir, "SegmentationClassNpy", base + ".npy"
            )
        if not noviz:
            out_clsv_file = osp.join(
                output_dir,
                "SegmentationClassVisualization",
                base + ".jpg",
            )
            # 添加边缘线可视化的输出文件路径
            out_clsv2_file = osp.join(
                output_dir,
                "SegmentationClassVisualization2",
                base + ".jpg",
            )
        if not noobject:
            out_insp_file = osp.join(
                output_dir, "SegmentationObject", base + ".png"
            )
            if not nonpy:
                out_ins_file = osp.join(
                    output_dir, "SegmentationObjectNpy", base + ".npy"
                )
            if not noviz:
                out_insv_file = osp.join(
                    output_dir,
                    "SegmentationObjectVisualization",
                    base + ".jpg",
                )

        img = labelme.utils.img_data_to_arr(label_file.imageData)
        imgviz.io.imsave(out_img_file, img)

        # 打印转换前的信息
        if debug:
            print(f"转换前: img.shape={img.shape}, shapes={label_file.shapes}")

        cls, ins = labelme.utils.shapes_to_label(
            img_shape=img.shape,
            shapes=label_file.shapes,
            label_name_to_value=class_name_to_id,
        )

        # 打印转换后的信息
        if debug:
            unique_cls = np.unique(cls)
            print(f"转换后: cls的唯一值={unique_cls}")
            # 检查具体每个类别的像素数量
            for class_id in range(-1, len(class_names)):
                pixel_count = np.sum(cls == class_id)
                class_name = "ignore" if class_id == -1 else class_names[class_id] if class_id >= 0 and class_id < len(class_names) else "未知"
                print(f"类别 {class_id} ({class_name}): {pixel_count} 像素")

        ins[cls == -1] = 0  # ignore it.

        # class label
        labelme.utils.lblsave(out_clsp_file, cls)
        if not nonpy:
            np.save(out_cls_file, cls)
        if not noviz:
            # 创建自定义颜色图
            colormap = np.zeros((len(class_names) + 1, 3), dtype=np.uint8)
            for i, name in enumerate(class_names):
                if name in class_colors:
                    colormap[i] = class_colors[name]
                else:
                    # 如果没有指定颜色，使用默认颜色
                    colormap[i] = (128, 128, 128)  # 默认为灰色

            # 使用原始图像作为背景，手动添加半透明的颜色覆盖
            # 首先复制原始图像
            clsv = img.copy()

            # 为每个类别添加半透明的颜色覆盖
            for i, name in enumerate(class_names):
                if i == 0:  # 跳过背景类别
                    continue

                # 获取当前类别的掩码
                mask = cls == i

                if np.any(mask):  # 如果存在这个类别的像素
                    # 获取该类别的颜色
                    color = colormap[i]

                    # 创建一个与原图相同大小的彩色覆盖层
                    overlay = np.zeros_like(clsv)
                    overlay[mask] = color

                    # 将覆盖层与原图混合（alpha=0.3表示30%不透明度）
                    alpha = 0.3
                    clsv[mask] = clsv[mask] * (1 - alpha) + overlay[mask] * alpha
            imgviz.io.imsave(out_clsv_file, clsv)

            # 创建边缘线可视化图像
            # 首先复制原始图像
            clsv2 = img.copy()

            # 导入OpenCV库用于边缘检测
            import cv2

            # 为每个类别绘制边缘线
            for i, name in enumerate(class_names):
                if i == 0:  # 跳过背景类别
                    continue

                # 获取当前类别的掩码
                mask = (cls == i).astype(np.uint8)

                if np.any(mask):  # 如果存在这个类别的像素
                    # 使用OpenCV找到边缘
                    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    # 获取该类别的颜色
                    color = colormap[i]
                    # OpenCV使用BGR顺序，而不是RGB，所以需要转换颜色顺序
                    bgr_color = (int(color[2]), int(color[1]), int(color[0]))

                    # 在原图上绘制边缘线
                    cv2.drawContours(clsv2, contours, -1, bgr_color, 2)  # 线宽为2

            # 保存边缘线可视化图像
            imgviz.io.imsave(out_clsv2_file, clsv2)

            # 另外保存一个纯色彩的掩码图像（不带原图背景）
            pure_mask = np.zeros((cls.shape[0], cls.shape[1], 3), dtype=np.uint8)
            for i in range(len(class_names)):
                mask = cls == i
                if i < len(colormap):
                    pure_mask[mask] = colormap[i]

            pure_mask_file = osp.join(
                output_dir,
                "SegmentationClassColor",
                base + ".png",
            )
            imgviz.io.imsave(pure_mask_file, pure_mask)

        if not noobject:
            # instance label
            labelme.utils.lblsave(out_insp_file, ins)
            if not nonpy:
                np.save(out_ins_file, ins)
            if not noviz:
                # 可以为实例分割也添加自定义颜色
                instance_ids = np.unique(ins)

                # 使用原始图像作为背景，手动添加半透明的颜色覆盖
                # 首先复制原始图像
                insv = img.copy()

                # 为每个实例添加半透明的颜色覆盖
                for i in instance_ids:
                    if i == 0:  # 跳过背景
                        continue

                    # 获取当前实例的掩码
                    mask = ins == i

                    if np.any(mask):  # 如果存在这个实例的像素
                        # 为每个实例生成一个随机颜色
                        color = np.random.randint(0, 255, 3).tolist()

                        # 创建一个与原图相同大小的彩色覆盖层
                        overlay = np.zeros_like(insv)
                        overlay[mask] = color

                        # 将覆盖层与原图混合（alpha=0.3表示30%不透明度）
                        alpha = 0.5
                        insv[mask] = insv[mask] * (1 - alpha) + overlay[mask] * alpha
                imgviz.io.imsave(out_insv_file, insv)

    return True


def main():
    """
    保留原来的命令行接口，方便兼容旧代码
    """
    import argparse
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument("input_dir", help="Input annotated directory")
    parser.add_argument("output_dir", help="Output dataset directory")
    parser.add_argument(
        "--labels", help="Labels file or comma separated text", required=True
    )
    parser.add_argument(
        "--noobject", help="Flag not to generate object label", action="store_true"
    )
    parser.add_argument(
        "--nonpy", help="Flag not to generate .npy files", action="store_true"
    )
    parser.add_argument(
        "--noviz", help="Flag to disable visualization", action="store_true"
    )
    args = parser.parse_args()

    convert_labelme_to_voc(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        labels=args.labels,
        noobject=args.noobject,
        nonpy=args.nonpy,
        noviz=args.noviz
    )


# 在脚本开始执行时创建输出目录树
def create_output_dirs(output_dir, nonpy=False, noviz=False, noobject=False):
    """创建必要的输出目录结构"""
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(osp.join(output_dir, "JPEGImages"), exist_ok=True)
    os.makedirs(osp.join(output_dir, "SegmentationClass"), exist_ok=True)
    os.makedirs(osp.join(output_dir, "SegmentationClassColor"), exist_ok=True)  # 新增彩色掩码目录

    if not nonpy:
        os.makedirs(osp.join(output_dir, "SegmentationClassNpy"), exist_ok=True)
    if not noviz:
        os.makedirs(osp.join(output_dir, "SegmentationClassVisualization"), exist_ok=True)
        os.makedirs(osp.join(output_dir, "SegmentationClassVisualization2"), exist_ok=True)  # 新增边缘线可视化目录
    if not noobject:
        os.makedirs(osp.join(output_dir, "SegmentationObject"), exist_ok=True)
        if not nonpy:
            os.makedirs(osp.join(output_dir, "SegmentationObjectNpy"), exist_ok=True)
        if not noviz:
            os.makedirs(osp.join(output_dir, "SegmentationObjectVisualization"), exist_ok=True)


# 示例用法
if __name__ == "__main__":
    convert_labelme_to_voc(
        input_dir=r'image_annotator2\split_1\mask2',
        output_dir="./test",
        labels=r"image_annotator2\src\labels.txt",
        noobject=True,
        nonpy=True,
        noviz=False,
        debug=True,  # 启用调试信息
        force_overwrite=True  # 强制覆盖已存在的输出目录
    )
    
    
    # S_dir = r'image_annotator2\split_1\mask2'
    # T_dir = r'./test'
    # os.makedirs(T_dir, exist_ok=True)
    # file_name = os.listdir(S_dir)
    
    # wrong_info = []
    
    
    # for name in file_name:
    #     file_path = os.path.join(S_dir, name)
    #     output_path = os.path.join(T_dir, name)
    #     try:
    #         convert_labelme_to_voc(
    #             input_dir=file_path,
    #             output_dir=output_path,
    #             labels=r"image_annotator2\src\labels.txt",
    #             noobject=True,
    #             nonpy=True,
    #             noviz=False,
    #             debug=False,  # 启用调试信息
    #             force_overwrite=True  # 强制覆盖已存在的输出目录
    #         )
    #     except Exception as e:
    #         print(f"Error processing {name}: {e}")
    #         print("出错的文件夹:", file_path)
    #         wrong_info.append(name)
            
    # print("出错的文件夹:", wrong_info)
    # with open(r'D:\code\操作教程\Src\wrong_info.txt', 'w') as f:
    #     f.write('\n'.join(wrong_info))

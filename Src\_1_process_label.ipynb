{"cells": [{"cell_type": "code", "execution_count": 1, "id": "32f2c11b", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "67a65b59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total records: 5159\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>visual_140439-Exam1_0156_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>visual_140439-Exam1_0158_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>visual_140439-Exam1_0164_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>visual_140439-Exam1_0165_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>visual_140439-Exam1_0166_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             filename   label\n", "0  visual_140439-Exam1_0156_image.png  redraw\n", "1  visual_140439-Exam1_0158_image.png  redraw\n", "2  visual_140439-Exam1_0164_image.png  redraw\n", "3  visual_140439-Exam1_0165_image.png  redraw\n", "4  visual_140439-Exam1_0166_image.png  redraw"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "\n", "base_dir = r'../label'\n", "os.listdir(base_dir)\n", "\n", "# 合并为一个json，并转换为pandas类型\n", "all_data = {}\n", "for file_name in os.listdir(base_dir):\n", "    file_path = os.path.join(base_dir, file_name)\n", "    if file_path.endswith('.json'):\n", "        with open(file_path, 'r') as f:\n", "            data = json.load(f)\n", "            all_data.update(data)\n", "\n", "# 转换为DataFrame\n", "df = pd.DataFrame(list(all_data.items()), columns=['filename', 'label'])\n", "\n", "# 显示结果\n", "print(f\"Total records: {len(df)}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "c46a6aff", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pic_name</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>140439-Exam1_0129_image.png</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>140439-Exam1_0130_image.png</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>140439-Exam1_0131_image.png</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>140439-Exam1_0132_image.png</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>140439-Exam1_0133_image.png</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7006</th>\n", "      <td>X375068-Exam3_0554_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7007</th>\n", "      <td>X375068-Exam3_0569_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7008</th>\n", "      <td>X375068-Exam3_0570_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7009</th>\n", "      <td>X375068-Exam3_0571_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7010</th>\n", "      <td>X375068-Exam3_0592_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7011 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                          pic_name   label\n", "0      140439-Exam1_0129_image.png     NaN\n", "1      140439-Exam1_0130_image.png     NaN\n", "2      140439-Exam1_0131_image.png     NaN\n", "3      140439-Exam1_0132_image.png     NaN\n", "4      140439-Exam1_0133_image.png     NaN\n", "...                            ...     ...\n", "7006  X375068-Exam3_0554_image.png  redraw\n", "7007  X375068-Exam3_0569_image.png  redraw\n", "7008  X375068-Exam3_0570_image.png  redraw\n", "7009  X375068-Exam3_0571_image.png  redraw\n", "7010  X375068-Exam3_0592_image.png  redraw\n", "\n", "[7011 rows x 2 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pic_dir = r'D:\\code\\操作教程2\\Data\\need_to_predict'\n", "pic_list = os.listdir(pic_dir)\n", "\n", "data = pd.DataFrame({'pic_name': pic_list})\n", "\n", "data['label'] = data['pic_name'].map(lambda x:\"visual_\"+x)\n", "\n", "map_dic = dict(zip(df['filename'],df['label']))\n", "\n", "data['label'] = data['label'].map(map_dic)\n", "data"]}, {"cell_type": "code", "execution_count": 4, "id": "b91db106", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pic_name</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>140439-Exam1_0129_image.png</td>\n", "      <td>stay</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>140439-Exam1_0130_image.png</td>\n", "      <td>stay</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>140439-Exam1_0131_image.png</td>\n", "      <td>stay</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>140439-Exam1_0132_image.png</td>\n", "      <td>stay</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>140439-Exam1_0133_image.png</td>\n", "      <td>stay</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7006</th>\n", "      <td>X375068-Exam3_0554_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7007</th>\n", "      <td>X375068-Exam3_0569_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7008</th>\n", "      <td>X375068-Exam3_0570_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7009</th>\n", "      <td>X375068-Exam3_0571_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7010</th>\n", "      <td>X375068-Exam3_0592_image.png</td>\n", "      <td>redraw</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7011 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                          pic_name   label\n", "0      140439-Exam1_0129_image.png    stay\n", "1      140439-Exam1_0130_image.png    stay\n", "2      140439-Exam1_0131_image.png    stay\n", "3      140439-Exam1_0132_image.png    stay\n", "4      140439-Exam1_0133_image.png    stay\n", "...                            ...     ...\n", "7006  X375068-Exam3_0554_image.png  redraw\n", "7007  X375068-Exam3_0569_image.png  redraw\n", "7008  X375068-Exam3_0570_image.png  redraw\n", "7009  X375068-Exam3_0571_image.png  redraw\n", "7010  X375068-Exam3_0592_image.png  redraw\n", "\n", "[7011 rows x 2 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data['label'] = data['label'].fillna(\"stay\")\n", "data"]}, {"cell_type": "code", "execution_count": 5, "id": "3d402a67", "metadata": {}, "outputs": [{"data": {"text/plain": ["label\n", "redraw    5014\n", "stay      1852\n", "delete     145\n", "Name: count, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data['label'].value_counts()"]}, {"cell_type": "code", "execution_count": 6, "id": "a82068a6", "metadata": {}, "outputs": [], "source": ["data.to_csv(r'D:\\code\\操作教程2\\Data\\label.csv',index=False)"]}, {"cell_type": "code", "execution_count": 7, "id": "a0c0c451", "metadata": {}, "outputs": [{"data": {"text/plain": ["pic_name\n", "20250328104623-Exam1    49\n", "375283-Exam1            46\n", "20250328103000-Exam1    39\n", "373627-Exam4            11\n", "Name: count, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data['label']=='delete']['pic_name'].map(lambda x:x.split('_')[0]).value_counts()"]}, {"cell_type": "code", "execution_count": 8, "id": "c20146dc", "metadata": {}, "outputs": [], "source": ["# 找到需要重新绘制的图片与掩码\n", "# import shutil\n", "# pic_names = data[data['label']=='redraw']['pic_name'].tolist()\n", "# pic_names\n", "\n", "# pic_dir = r'D:\\code\\操作教程2\\Data\\need_to_predict'\n", "# mask_dir = r'D:\\code\\操作教程2\\Data\\predicted_masks'\n", "\n", "# redraw_dir = r'D:\\code\\操作教程2\\Data\\redraw_pic'\n", "# os.makedirs(redraw_dir, exist_ok=True)\n", "# reference_dir = r'D:\\code\\操作教程2\\Data\\redraw_mask'\n", "# os.makedirs(reference_dir, exist_ok=True)\n", "\n", "# for pic_name in pic_names:\n", "#     pic_path = os.path.join(pic_dir, pic_name)\n", "#     shutil.copy(pic_path, redraw_dir)\n", "    \n", "#     mask_path = os.path.join(mask_dir, pic_name.replace('_image',\"_mask\"))\n", "#     shutil.copy(mask_path, reference_dir)"]}, {"cell_type": "code", "execution_count": 5, "id": "07b4607c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["处理图像: 100%|██████████| 5014/5014 [01:32<00:00, 54.23it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["所有图像处理完成，结果保存在: ../Data/contour_mask\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import os\n", "import cv2\n", "import numpy as np\n", "from tqdm import tqdm\n", "\n", "# 设置目录路径\n", "redraw_dir = '../Data/redraw_mask'  # 包含需要重绘掩码的目录\n", "image_dir = '../Data/redraw_pic'    # 包含原始图像的目录\n", "output_dir = '../Data/contour_mask'  # 输出轮廓图像的目录\n", "\n", "# 创建输出目录（如果不存在）\n", "if not os.path.exists(output_dir):\n", "    os.makedirs(output_dir)\n", "\n", "# 处理所有掩码文件\n", "for filename in tqdm(os.listdir(redraw_dir), desc=\"处理图像\"):\n", "    if filename.endswith('.png') or filename.endswith('.jpg'):\n", "        # 构建文件路径\n", "        mask_path = os.path.join(redraw_dir, filename)\n", "        img_path = os.path.join(image_dir, filename.replace('_mask', '_image'))\n", "        output_path = os.path.join(output_dir, filename.replace('_mask', '_contour'))\n", "        \n", "        # 检查文件是否存在\n", "        if not os.path.exists(img_path):\n", "            print(f\"警告: 原始图像不存在: {img_path}\")\n", "            continue\n", "        \n", "        # 读取图像\n", "        original_img = cv2.imread(img_path)\n", "        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)\n", "        \n", "        if original_img is None or mask is None:\n", "            print(f\"警告: 无法读取图像: {filename}\")\n", "            continue\n", "        \n", "        # 创建原始图像与掩码的叠加效果（使用轮廓线）\n", "        combined = original_img.copy()\n", "        \n", "        # 为128值区域创建轮廓（内部区域 - 绿色）\n", "        mask_128 = np.zeros_like(mask)\n", "        mask_128[mask == 128] = 255\n", "        contours_128, _ = cv2.findContours(mask_128, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "        cv2.drawContours(combined, contours_128, -1, (0, 255, 0), 2)  # 绿色轮廓\n", "        \n", "        # 为255值区域创建轮廓（外部区域 - 红色）\n", "        mask_255 = np.zeros_like(mask)\n", "        mask_255[mask == 255] = 255\n", "        contours_255, _ = cv2.findContours(mask_255, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n", "        cv2.drawContours(combined, contours_255, -1, (0, 0, 255), 2)  # 红色轮廓\n", "        \n", "        # 保存结果\n", "        cv2.imwrite(output_path, combined)\n", "\n", "print(f\"所有图像处理完成，结果保存在: {output_dir}\")"]}], "metadata": {"kernelspec": {"display_name": "deeplearn", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新分配 split_4 目录中的文件到5个新的目录中
将原来分配给一个人的标注任务重新平均分配给5个人
"""

import os
import shutil
import math
from pathlib import Path

def redistribute_files():
    """
    将 split_4 目录中的文件重新分配到 split_4_1 到 split_4_5 这5个目录中
    """
    # 源目录
    source_dir = Path("image_annotator2/split_4")
    mask_dir = source_dir / "mask"
    pic_dir = source_dir / "pic"
    
    # 检查源目录是否存在
    if not source_dir.exists():
        print(f"错误：源目录 {source_dir} 不存在")
        return False
    
    if not mask_dir.exists() or not pic_dir.exists():
        print(f"错误：mask 或 pic 目录不存在")
        return False
    
    # 获取所有mask文件
    mask_files = list(mask_dir.glob("*.png"))
    mask_files.sort()  # 排序确保一致性
    
    print(f"找到 {len(mask_files)} 个mask文件")
    
    # 计算每个目录应该分配多少文件
    total_files = len(mask_files)
    files_per_split = math.ceil(total_files / 5)
    
    print(f"总共 {total_files} 个文件，将分配到5个目录中")
    print(f"每个目录大约 {files_per_split} 个文件")
    
    # 创建5个新目录
    for i in range(1, 6):
        new_dir = Path(f"image_annotator2/split_4_{i}")
        new_mask_dir = new_dir / "mask"
        new_pic_dir = new_dir / "pic"
        
        # 创建目录结构
        new_mask_dir.mkdir(parents=True, exist_ok=True)
        new_pic_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"创建目录: {new_dir}")
    
    # 分配文件
    for i, mask_file in enumerate(mask_files):
        # 确定这个文件应该分配到哪个目录 (1-5)
        split_index = (i // files_per_split) + 1
        if split_index > 5:  # 确保不超过5
            split_index = 5
        
        # 构建目标路径
        target_dir = Path(f"image_annotator2/split_4_{split_index}")
        target_mask_dir = target_dir / "mask"
        target_pic_dir = target_dir / "pic"
        
        # 复制mask文件
        target_mask_file = target_mask_dir / mask_file.name
        shutil.copy2(mask_file, target_mask_file)
        
        # 找到对应的pic文件并复制
        # mask文件名格式: xxxxx_contour.png
        # pic文件名格式: xxxxx_image.png
        pic_filename = mask_file.name.replace("_contour.png", "_image.png")
        pic_file = pic_dir / pic_filename
        
        if pic_file.exists():
            target_pic_file = target_pic_dir / pic_filename
            shutil.copy2(pic_file, target_pic_file)
        else:
            print(f"警告：找不到对应的图片文件 {pic_filename}")
        
        # 显示进度
        if (i + 1) % 50 == 0 or i == len(mask_files) - 1:
            print(f"已处理 {i + 1}/{len(mask_files)} 个文件")
    
    # 统计每个目录的文件数量
    print("\n分配结果:")
    total_distributed = 0
    for i in range(1, 6):
        split_dir = Path(f"image_annotator2/split_4_{i}")
        mask_count = len(list((split_dir / "mask").glob("*.png")))
        pic_count = len(list((split_dir / "pic").glob("*.png")))
        total_distributed += mask_count
        print(f"split_4_{i}: {mask_count} 个mask文件, {pic_count} 个pic文件")
    
    print(f"\n总计分配了 {total_distributed} 个文件")
    print("重新分配完成！")
    
    return True

def main():
    """主函数"""
    print("开始重新分配 split_4 目录中的文件...")
    print("将原来的文件分配到 split_4_1 到 split_4_5 这5个目录中")
    print()
    
    # 确认操作
    response = input("确认要执行重新分配操作吗？(y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    # 执行重新分配
    success = redistribute_files()
    
    if success:
        print("\n重新分配成功完成！")
        print("现在你可以将 split_4_1 到 split_4_5 这5个目录分配给不同的标注人员。")
    else:
        print("\n重新分配失败，请检查错误信息。")

if __name__ == "__main__":
    main()

import os
import cv2
import numpy as np
from tqdm import tqdm

def visualize_images(data_dir, output_dir):
    """
    可视化图像、预测掩码和参考掩码，并将结果保存到输出目录
    
    参数:
        data_dir: 数据根目录
        output_dir: 可视化结果输出目录
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有需要处理的图像文件名
    image_dir = os.path.join(data_dir, "need_to_predict")
    pred_mask_dir = os.path.join(data_dir, "predicted_masks")
    ref_mask_dir = os.path.join(data_dir, "right_data")
    
    # 获取图像文件列表
    image_files = [f for f in os.listdir(image_dir) if f.endswith('.png')]
    print(f"找到 {len(image_files)} 个图像文件")
    
    # 处理每个图像
    for img_file in tqdm(image_files, desc="处理图像"):
        # 构建文件路径
        img_path = os.path.join(image_dir, img_file)
        pred_mask_path = os.path.join(pred_mask_dir, img_file.replace('_image', '_mask'))
        ref_mask_path = os.path.join(ref_mask_dir, img_file)
        
        # 检查文件是否存在
        if not os.path.exists(pred_mask_path):
            print(f"警告: 预测掩码不存在: {pred_mask_path}")
            continue
        if not os.path.exists(ref_mask_path):
            print(f"警告: 参考掩码不存在: {ref_mask_path}")
            continue
        
        # 读取图像
        original_img = cv2.imread(img_path)
        pred_mask = cv2.imread(pred_mask_path)
        ref_mask = cv2.imread(ref_mask_path)
        
        if original_img is None or pred_mask is None or ref_mask is None:
            print(f"警告: 无法读取图像: {img_file}")
            continue
        
        # 调整所有图像大小以确保一致性
        height, width = original_img.shape[:2]
        pred_mask = cv2.resize(pred_mask, (width, height))
        ref_mask = cv2.resize(ref_mask, (width, height))
        
        # 将预测掩码转换为灰度图像
        if len(pred_mask.shape) == 3:
            pred_mask_gray = cv2.cvtColor(pred_mask, cv2.COLOR_BGR2GRAY)
        else:
            pred_mask_gray = pred_mask
        
        # 创建原始图像与预测掩码的叠加效果（使用轮廓线）
        combined = original_img.copy()
        
        # 为128值区域创建轮廓（内部区域 - 绿色）
        mask_128 = np.zeros_like(pred_mask_gray)
        mask_128[pred_mask_gray == 128] = 255
        contours_128, _ = cv2.findContours(mask_128, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(combined, contours_128, -1, (0, 255, 0), 2)  # 绿色轮廓
        
        # 为255值区域创建轮廓（外部区域 - 红色）
        mask_255 = np.zeros_like(pred_mask_gray)
        mask_255[pred_mask_gray == 255] = 255
        contours_255, _ = cv2.findContours(mask_255, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(combined, contours_255, -1, (0, 0, 255), 2)  # 红色轮廓
        
        # 创建2x2网格
        grid = np.zeros((height*2, width*2, 3), dtype=np.uint8)
        
        # 放置图像到网格
        grid[:height, :width] = original_img  # 左上: 原始图像
        grid[:height, width:] = pred_mask     # 右上: 预测掩码（原始灰度）
        grid[height:, :width] = ref_mask      # 左下: 参考掩码
        grid[height:, width:] = combined      # 右下: 叠加效果（带轮廓线）
        
        # 添加标签
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(grid, "Original Image", (10, 30), font, 0.7, (255, 255, 255), 2)
        cv2.putText(grid, "Predicted Mask", (width+10, 30), font, 0.7, (255, 255, 255), 2)
        cv2.putText(grid, "Reference Mask", (10, height+30), font, 0.7, (255, 255, 255), 2)
        cv2.putText(grid, "Combined View", (width+10, height+30), font, 0.7, (255, 255, 255), 2)
        
        # 添加图例
        legend_y = height*2 - 60
        # 绿色类别（内部）
        cv2.rectangle(grid, (width*2-200, legend_y), (width*2-180, legend_y+20), (0, 255, 0), -1)
        cv2.putText(grid, "Class 1 (128)", (width*2-170, legend_y+15), font, 0.5, (255, 255, 255), 1)
        # 红色类别（外部）
        cv2.rectangle(grid, (width*2-200, legend_y+30), (width*2-180, legend_y+50), (0, 0, 255), -1)
        cv2.putText(grid, "Class 2 (255)", (width*2-170, legend_y+45), font, 0.5, (255, 255, 255), 1)
        
        # 保存结果
        output_path = os.path.join(output_dir, f"visual_{img_file}")
        cv2.imwrite(output_path, grid)
    
    print(f"可视化完成，结果保存在: {output_dir}")

if __name__ == "__main__":
    data_dir = r'Data'
    output_dir = os.path.join(data_dir, "visual_all")
    visualize_images(data_dir, output_dir)



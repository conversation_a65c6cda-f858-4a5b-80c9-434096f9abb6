{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pydicom\n", "r = \"D:\\DeskTop\\新建文件夹\\2890098\\Exam1\\0QKAL2T4\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset.file_meta -------------------------------\n", "(0002,0000) File Meta Information Group Length  UL: 220\n", "(0002,0001) File Meta Information Version       OB: b'\\x00\\x01'\n", "(0002,0002) Media Storage SOP Class UID         UI: Ultrasound Multi-frame Image Storage\n", "(0002,0003) Media Storage SOP Instance UID      UI: 1.2.276.0.7230010.3.1.4.8323329.1621.1751861866.212178\n", "(0002,0010) Transfer Syntax UID                 UI: Explicit VR Little Endian\n", "(0002,0012) Implementation Class UID            UI: 1.2.276.0.7230010.3.0.3.6.2\n", "(0002,0013) Implementation Version Name         SH: 'OFFIS_DCMTK_362'\n", "(0002,0016) Source Application Entity Title     AE: 'INSIGHT_SCU'\n", "-------------------------------------------------\n", "(0008,0005) Specific Character Set              CS: 'ISO_IR 192'\n", "(0008,0008) Image Type                          CS: ['ORIGINAL', 'PRIMARY', 'INTRAVASCULAR']\n", "(0008,0016) SOP Class UID                       UI: Ultrasound Multi-frame Image Storage\n", "(0008,0018) SOP Instance UID                    UI: 1.2.276.0.7230010.3.1.4.8323329.1621.1751861866.212178\n", "(0008,0020) Study Date                          DA: '20250618'\n", "(0008,0021) Series Date                         DA: '20250618'\n", "(0008,0022) Acquisition Date                    DA: '20250618'\n", "(0008,0023) Content Date                        DA: '20250618'\n", "(0008,002A) Acquisition DateTime                DT: '20250618181548.981000'\n", "(0008,0030) Study Time                          TM: '181548'\n", "(0008,0031) Series Time                         TM: '181548'\n", "(0008,0032) Acquisition Time                    TM: '181548.981000'\n", "(0008,0033) Content Time                        TM: '181548'\n", "(0008,0050) Accession Number                    SH: ''\n", "(0008,0060) Modality                            CS: 'IVUS'\n", "(0008,0070) Manufacturer                        LO: 'Insight Lifetech'\n", "(0008,0080) Institution Name                    LO: ''\n", "(0008,0081) Institution Address                 ST: ''\n", "(0008,0090) Referring Physician's Name          PN: ''\n", "(0008,1010) Station Name                        SH: 'IVUS-PC'\n", "(0008,1030) Study Description                   LO: ''\n", "(0008,103E) Series Description                  LO: 'Exam1'\n", "(0008,1050) Performing Physician's Name         PN: ''\n", "(0008,1090) Manufacturer's Model Name           LO: 'VivoHeart'\n", "(0008,2142) Start Trim                          IS: '1'\n", "(0008,2143) Stop Trim                           IS: '1593'\n", "(0008,2144) Recommended Display Frame Rate      IS: '30'\n", "(000B,0010) Private Creator                     CS: ''\n", "(000B,1010) Private tag data                    CS: '655617'\n", "(000B,1020) Private tag data                    CS: '0'\n", "(000B,1030) Private tag data                    DS: '3.0'\n", "(000B,1040) Private tag data                    DS: '60'\n", "(0010,0010) <PERSON><PERSON>'s Name                      PN: '刘志强'\n", "(0010,0020) Patient ID                          LO: '2890098'\n", "(0010,0030) <PERSON><PERSON>'s Birth Date                DA: '20250618'\n", "(0010,0040) Patient's Sex                       CS: 'O'\n", "(0010,21B0) Additional Patient History          LT: ''\n", "(0011,0010) Private Creator                     DT: '20250618172654'\n", "(0011,0020) Private Creator                     IS: '0'\n", "(0011,0030) Private Creator                     LO: '0'\n", "(0018,0040) Cine Rate                           IS: '30'\n", "(0018,1000) Device Serial Number                LO: '1'\n", "(0018,1020) Software Versions                   LO: '[V1.1.13.2] [10890]'\n", "(0018,1065) Frame Time Vector                   DS: [0.00, 33.65, 33.65]\n", "(0018,1066) Frame Delay                         DS: '0'\n", "(0018,106A) Synchronization Trigger             CS: 'NO TRIGGER'\n", "(0018,1800) Acquisition Time Synchronized       CS: 'Y'\n", "(0018,3100) IVUS Acquisition                    CS: 'MOTOR_PULLBACK'\n", "(0018,3101) IVUS Pullback Rate                  DS: '3.0'\n", "(0018,3103) IVUS Pullback Start Frame Number    IS: '1'\n", "(0018,3104) IVUS Pullback Stop Frame Number     IS: '1593'\n", "(0018,5010) Transducer Data                     LO: 'TrueVision'\n", "(0018,6011)  Sequence of Ultrasound Regions  1 item(s) ---- \n", "   (0018,6012) Region Spatial Format               US: 1\n", "   (0018,6014) Region Data Type                    US: 1\n", "   (0018,6016) Region Flags                        UL: 0\n", "   (0018,6018) Region Location Min X0              UL: 0\n", "   (0018,601A) Region Location Min Y0              UL: 0\n", "   (0018,601C) Region Location Max X1              UL: 511\n", "   (0018,601E) Region Location Max Y1              UL: 511\n", "   (0018,6024) Physical Units X Direction          US: 3\n", "   (0018,6026) Physical Units Y Direction          US: 3\n", "   (0018,602C) Physical Delta X                    FD: 0.00195312\n", "   (0018,602E) Physical Delta Y                    FD: 0.00195312\n", "   ---------\n", "(0018,6031) Transducer Type                     CS: 'IV_ROT XTAL'\n", "(0020,000D) Study Instance UID                  UI: 1.2.276.0.7230010.3.1.2.20250618172654.518331411\n", "(0020,000E) Series Instance UID                 UI: 1.2.276.0.7230010.3.1.2.20250618172654.518331411181548\n", "(0020,0010) Study ID                            SH: '2025061820250618'\n", "(0020,0011) Series Number                       IS: '1'\n", "(0020,0013) Instance Number                     IS: '1'\n", "(0020,0020) Patient Orientation                 CS: ''\n", "(0020,0060) Laterality                          CS: ''\n", "(0020,0200) Synchronization Frame of Reference  UI: Universal Coordinated Time\n", "(0020,4000) Image Comments                      LT: 'Exam1;;LAD'\n", "(0021,1027)  Private tag data  2 item(s) ---- \n", "   (0021,0010) Private Creator                     LO: '457'\n", "   (0021,0020) Private Creator                     LO: '2'\n", "   (0021,0030) Private Creator                     LO: '215'\n", "   (0021,0040) Private Creator                     LO: '7'\n", "   (0021,0050) Private Creator                     LO: '600'\n", "   (0021,0060) Private Creator                     LO: '600'\n", "   (0021,0070) Private Creator                     LO: '321'\n", "   (0021,0080) Private Creator                     LT: Array of 1283 elements\n", "   (0021,0090) Private Creator                     LT: Array of 1283 elements\n", "   (0021,00A0) Private Creator                     LO: 'A1'\n", "   (0021,00B0) Private Creator                     LO: '2.61347'\n", "   (0021,00C0) Private Creator                     LO: 'mm*mm'\n", "   (0021,00D0) Private Creator                     LO: 'FAE652'\n", "   (0021,00E0) Private Creator                     LO: '20250618181622'\n", "   (0021,0100) Private tag data                    LO: '32640'\n", "   ---------\n", "   (0021,0010) Private Creator                     LO: '457'\n", "   (0021,0020) Private Creator                     LO: '2'\n", "   (0021,0030) Private Creator                     LO: '215'\n", "   (0021,0040) Private Creator                     LO: '7'\n", "   (0021,0050) Private Creator                     LO: '600'\n", "   (0021,0060) Private Creator                     LO: '600'\n", "   (0021,0070) Private Creator                     LO: '575'\n", "   (0021,0080) Private Creator                     LT: Array of 2299 elements\n", "   (0021,0090) Private Creator                     LT: Array of 2299 elements\n", "   (0021,00A0) Private Creator                     LO: 'A2'\n", "   (0021,00B0) Private Creator                     LO: '8.34153'\n", "   (0021,00C0) Private Creator                     LO: 'mm*mm'\n", "   (0021,00D0) Private Creator                     LO: '00C763'\n", "   (0021,00E0) Private Creator                     LO: '20250618181622'\n", "   (0021,0100) Private tag data                    LO: '0'\n", "   ---------\n", "(0021,1028)  Private tag data  7 item(s) ---- \n", "   (0021,0010) Private Creator                     LO: '457'\n", "   (0021,0020) Private Creator                     LO: '215'\n", "   (0021,0030) Private Creator                     LO: '7'\n", "   (0021,0040) Private Creator                     LO: '600'\n", "   (0021,0050) Private Creator                     LO: '600'\n", "   (0021,0060) Private Creator                     LO: '480'\n", "   (0021,0070) Private Creator                     LO: '269'\n", "   (0021,0080) Private Creator                     LO: '565'\n", "   (0021,0090) Private Creator                     LO: '315'\n", "   (0021,00A0) Private Creator                     LO: 'Min'\n", "   (0021,00B0) Private Creator                     LO: '1.61081'\n", "   (0021,00C0) Private Creator                     LO: 'mm'\n", "   (0021,00D0) Private Creator                     LO: 'FAE652'\n", "   (0021,00E0) Private Creator                     LO: '20250618181622'\n", "   ---------\n", "   (0021,0010) Private Creator                     LO: '457'\n", "   (0021,0020) Private Creator                     LO: '215'\n", "   (0021,0030) Private Creator                     LO: '7'\n", "   (0021,0040) Private Creator                     LO: '600'\n", "   (0021,0050) Private Creator                     LO: '600'\n", "   (0021,0060) Private Creator                     LO: '511'\n", "   (0021,0070) Private Creator                     LO: '350'\n", "   (0021,0080) Private Creator                     LO: '531'\n", "   (0021,0090) Private Creator                     LO: '232'\n", "   (0021,00A0) Private Creator                     LO: 'Max'\n", "   (0021,00B0) Private Creator                     LO: '1.99472'\n", "   (0021,00C0) Private Creator                     LO: 'mm'\n", "   (0021,00D0) Private Creator                     LO: 'FAE652'\n", "   (0021,00E0) Private Creator                     LO: '20250618181622'\n", "   ---------\n", "   (0021,0010) Private Creator                     LO: '457'\n", "   (0021,0020) Private Creator                     LO: '215'\n", "   (0021,0030) Private Creator                     LO: '7'\n", "   (0021,0040) Private Creator                     LO: '600'\n", "   (0021,0050) Private Creator                     LO: '600'\n", "   (0021,0060) Private Creator                     LO: '395'\n", "   (0021,0070) Private Creator                     LO: '271'\n", "   (0021,0080) Private Creator                     LO: '573'\n", "   (0021,0090) Private Creator                     LO: '317'\n", "   (0021,00A0) Private Creator                     LO: 'Min'\n", "   (0021,00B0) Private Creator                     LO: '3.06413'\n", "   (0021,00C0) Private Creator                     LO: 'mm'\n", "   (0021,00D0) Private Creator                     LO: '00C763'\n", "   (0021,00E0) Private Creator                     LO: '20250618181622'\n", "   ---------\n", "   (0021,0010) Private Creator                     LO: '457'\n", "   (0021,0020) Private Creator                     LO: '215'\n", "   (0021,0030) Private Creator                     LO: '7'\n", "   (0021,0040) Private Creator                     LO: '600'\n", "   (0021,0050) Private Creator                     LO: '600'\n", "   (0021,0060) Private Creator                     LO: '445'\n", "   (0021,0070) Private Creator                     LO: '397'\n", "   (0021,0080) Private Creator                     LO: '517'\n", "   (0021,0090) Private Creator                     LO: '202'\n", "   (0021,00A0) Private Creator                     LO: 'Max'\n", "   (0021,00B0) Private Creator                     LO: '3.46446'\n", "   (0021,00C0) Private Creator                     LO: 'mm'\n", "   (0021,00D0) Private Creator                     LO: '00C763'\n", "   (0021,00E0) Private Creator                     LO: '20250618181622'\n", "   ---------\n", "   (0021,0010) Private Creator                     LO: '304'\n", "   (0021,0020) Private Creator                     LO: '5'\n", "   (0021,0030) Private Creator                     LO: '7'\n", "   (0021,0040) Private Creator                     LO: '450'\n", "   (0021,0050) Private Creator                     LO: '450'\n", "   (0021,0060) Private Creator                     LO: '195'\n", "   (0021,0070) Private Creator                     LO: '303'\n", "   (0021,0080) Private Creator                     LO: '169'\n", "   (0021,0090) Private Creator                     LO: '183'\n", "   (0021,00A0) Private Creator                     LO: 'D1'\n", "   (0021,00B0) Private Creator                     LO: '2.72854'\n", "   (0021,00C0) Private Creator                     LO: 'mm'\n", "   (0021,00D0) Private Creator                     LO: '55B7F0'\n", "   (0021,00E0) Private Creator                     LO: '20250618181651'\n", "   ---------\n", "   (0021,0010) Private Creator                     LO: '304'\n", "   (0021,0020) Private Creator                     LO: '5'\n", "   (0021,0030) Private Creator                     LO: '7'\n", "   (0021,0040) Private Creator                     LO: '450'\n", "   (0021,0050) Private Creator                     LO: '450'\n", "   (0021,0060) Private Creator                     LO: '261'\n", "   (0021,0070) Private Creator                     LO: '242'\n", "   (0021,0080) Private Creator                     LO: '122'\n", "   (0021,0090) Private Creator                     LO: '247'\n", "   (0021,00A0) Private Creator                     LO: 'D2'\n", "   (0021,00B0) Private Creator                     LO: '3.09089'\n", "   (0021,00C0) Private Creator                     LO: 'mm'\n", "   (0021,00D0) Private Creator                     LO: 'F775A5'\n", "   (0021,00E0) Private Creator                     LO: '20250618181655'\n", "   ---------\n", "   (0021,0010) Private Creator                     LO: '1109'\n", "   (0021,0020) Private Creator                     LO: '5'\n", "   (0021,0030) Private Creator                     LO: '7'\n", "   (0021,0040) Private Creator                     LO: '450'\n", "   (0021,0050) Private Creator                     LO: '450'\n", "   (0021,0060) Private Creator                     LO: '323'\n", "   (0021,0070) Private Creator                     LO: '289'\n", "   (0021,0080) Private Creator                     LO: '135'\n", "   (0021,0090) Private Creator                     LO: '264'\n", "   (0021,00A0) Private Creator                     LO: 'D1'\n", "   (0021,00B0) Private Creator                     LO: '4.21455'\n", "   (0021,00C0) Private Creator                     LO: 'mm'\n", "   (0021,00D0) Private Creator                     LO: '55B7F0'\n", "   (0021,00E0) Private Creator                     LO: '20250618181723'\n", "   ---------\n", "(0028,0002) Samples per Pixel                   US: 1\n", "(0028,0004) Photometric Interpretation          CS: 'MONOCHROME2'\n", "(0028,0008) Number of Frames                    IS: '1593'\n", "(0028,0009) Frame Increment Pointer             AT: (0018,1065)\n", "(0028,0010) Rows                                US: 512\n", "(0028,0011) Columns                             US: 512\n", "(0028,0030) Pixel Spacing                       DS: [0.0195312, 0.0195312]\n", "(0028,0100) Bits Allocated                      US: 8\n", "(0028,0101) Bits Stored                         US: 8\n", "(0028,0102) High Bit                            US: 7\n", "(0028,0103) Pixel Representation                US: 0\n", "(0028,2110) Lossy Image Compression             CS: '00'\n", "(0028,6020) Frame Numbers of Interest (FOI)     US: [445, 1208, 458, 305, 1110]\n", "(0028,6022) Frame of Interest Description       LO: ['B1', 'B2', 'B3', 'B4', 'B5']\n", "(0055,0010) Private Creator                     IS: '604383040'\n", "(0055,0020) Private Creator                     LO: 'OMO-B021250401-6000-849-093'\n", "(0055,0030) Private Creator                     IS: '5'\n", "(0055,0040) Private Creator                     IS: '60'\n", "(0055,0050) Private Creator                     IS: '0'\n", "(0055,0060) Private Creator                     IS: '131'\n", "(0055,0070) Private Creator                     IS: '4'\n", "(0055,0080) Private Creator                     IS: '1'\n", "(0055,0090) Private Creator                     IS: '1'\n", "(0055,00A0) Private Creator                     LO: '[V1.1.13.2] [10890]'\n", "(7FE0,0010) Pixel Data                          OB: Array of 417595392 elements"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import pydicom\n", "\n", "# 指定DICOM文件夹路径\n", "r = r\"D:\\DeskTop\\新建文件夹\\2890098\\Exam1\\0QKAL2T4\"\n", "\n", "ds = pydicom.dcmread(r)\n", "ds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "deeplearn", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}
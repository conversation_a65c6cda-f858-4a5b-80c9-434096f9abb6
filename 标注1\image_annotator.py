import sys
import os
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QFileDialog, QListWidget)
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt

class ImageAnnotator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('图像标注工具')
        self.setGeometry(100, 100, 2000, 1400)  # 加大初始窗口尺寸

        # 放中间
        self.move(700, 300)
        
        # 初始化变量
        self.current_folder = None
        self.image_files = []
        self.current_image_index = -1
        self.annotations = {}  # 存储标注结果
        self.patient_name = ""  # 病人名字

        # 设置窗口焦点策略
        self.setFocusPolicy(Qt.StrongFocus)
        
        self.init_ui()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        central_widget.setFocusPolicy(Qt.StrongFocus)  # 设置中央窗口的焦点策略
        main_layout = QHBoxLayout()

        # 左侧布局
        left_layout = QVBoxLayout()
        
        self.folder_btn = QPushButton('选择图片文件夹')
        self.folder_btn.clicked.connect(self.select_folder)
        left_layout.addWidget(self.folder_btn)

        self.file_list = QListWidget()
        self.file_list.itemClicked.connect(self.show_image)
        left_layout.addWidget(self.file_list)

        self.save_btn = QPushButton('保存标注结果')
        self.save_btn.clicked.connect(self.save_annotations)
        left_layout.addWidget(self.save_btn)

        # 右侧布局（图片显示区域）
        right_layout = QVBoxLayout()
        
        # 图片显示在中间
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(1000,1000)  # 设置最小尺寸
        right_layout.addWidget(self.image_label)

        # 状态标签显示在图片下方
        status_container = QHBoxLayout()
        status_container.addStretch()
        self.status_label = QLabel('标注状态：未标注')
        self.status_label.setStyleSheet("QLabel { font-size: 14pt; }")  # 加大字体
        status_container.addWidget(self.status_label)
        status_container.addStretch()
        right_layout.addLayout(status_container)

        # 快捷键说明
        shortcuts_label = QLabel(
            '快捷键说明：\n'
            '右键/右方向键/下方向键 - 下一张\n'
            '左方向键/上方向键 - 上一张\n'
            'C键 - 标记删除\n'
            'X键 - 标记修改\n'
            'Z键 - 撤销标注\n'
            'S键 - 保存标注\n'
            '默认状态为0（不操作）'
        )
        shortcuts_label.setStyleSheet("QLabel { font-size: 12pt; }")
        right_layout.addWidget(shortcuts_label)

        main_layout.addLayout(left_layout, 1)
        main_layout.addLayout(right_layout, 4)
        central_widget.setLayout(main_layout)

    def keyPressEvent(self, event):
        print(f"按键被按下: {event.key()}")  # 基本调试信息
        
        if event.key() == Qt.Key_S:  # S键保存标注
            print("保存标注")  # 调试信息
            self.save_annotations()
            return
        
        if self.current_image_index >= 0:
            current_item = self.file_list.item(self.current_image_index)
            if not current_item:
                return
            
            # 获取原始文件名（不包含状态标记）
            file_name = current_item.text().split(" [")[0]
            
            if event.key() == Qt.Key_C:  # C键标记删除
                print(f"标记删除: {file_name}")  # 调试信息
                self.annotations[file_name] = "delete"
                self.status_label.setText('标注状态：删除')
                self.status_label.setStyleSheet("QLabel { font-size: 14pt; color: red; }")
                self.update_list_item()
                self.next_image()
            elif event.key() == Qt.Key_X:  # X键标记修改
                print(f"标记修改: {file_name}")  # 调试信息
                self.annotations[file_name] = "redraw"
                self.status_label.setText('标注状态：修改')
                self.status_label.setStyleSheet("QLabel { font-size: 14pt; color: blue; }")
                self.update_list_item()
                self.next_image()
            elif event.key() == Qt.Key_Z:  # Z键撤销标注
                if file_name in self.annotations:
                    del self.annotations[file_name]
                    self.status_label.setText('标注状态：未标注')
                    self.status_label.setStyleSheet("QLabel { font-size: 14pt; color: black; }")
                    self.update_list_item()
            elif event.key() in (Qt.Key_Right, Qt.Key_Down):  # 右方向键或下方向键切换下一张
                print("下一张图片")  # 调试信息
                self.next_image()
            elif event.key() in (Qt.Key_Left, Qt.Key_Up):  # 左方向键或上方向键切换上一张
                print("上一张图片")  # 调试信息
                self.prev_image()
        
        # 确保事件被处理
        event.accept()

    def mousePressEvent(self, event):  # 改用mousePressEvent
        if event.button() == Qt.RightButton:
            self.next_image()

    def prev_image(self):
        if self.current_image_index > 0:
            self.current_image_index -= 1
            self.file_list.setCurrentRow(self.current_image_index)
            self.show_image(self.file_list.currentItem())

    def next_image(self):
        if self.current_image_index < len(self.image_files) - 1:
            self.current_image_index += 1
            self.file_list.setCurrentRow(self.current_image_index)
            self.show_image(self.file_list.currentItem())

    def select_folder(self):
        folder = QFileDialog.getExistingDirectory(self, '选择图片文件夹')
        if folder:
            self.current_folder = folder
            self.patient_name = os.path.basename(folder)
            self.image_files = [f for f in os.listdir(folder) 
                              if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            self.file_list.clear()
            self.file_list.addItems(self.image_files)
            self.annotations = {}  # 清空之前的标注
            
            if self.image_files:
                self.current_image_index = 0
                self.file_list.setCurrentRow(0)
                self.show_image(self.file_list.item(0))

    def show_image(self, item):
        if not item or not self.current_folder:
            return
        
        # 获取原始文件名（不包含状态标记）
        file_name = item.text().split(" [")[0]  # 分割并获取文件名部分
        image_path = os.path.join(self.current_folder, file_name)
        
        print(f"显示图片: {image_path}")  # 调试信息
        
        # 显示图片
        pixmap = QPixmap(image_path)
        scaled_pixmap = pixmap.scaled(self.image_label.size(), 
                                    Qt.KeepAspectRatio, 
                                    Qt.SmoothTransformation)
        self.image_label.setPixmap(scaled_pixmap)

        # 显示当前标注状态
        current_annotation = self.annotations.get(file_name, "0")  # 默认状态为0
        self.status_label.setText(f'标注状态：{current_annotation}')
        
        # 确保窗口有焦点
        self.setFocus()

    def update_list_item(self):
        if self.current_image_index >= 0:
            current_item = self.file_list.item(self.current_image_index)
            if not current_item:
                return
            
            # 获取原始文件名（不包含状态标记）
            file_name = current_item.text().split(" [")[0]
            annotation = self.annotations.get(file_name, "0")  # 默认状态为0
            
            # 设置不同状态的颜色
            if annotation == "delete":
                current_item.setForeground(Qt.red)
            elif annotation == "redraw":
                current_item.setForeground(Qt.blue)
            else:
                current_item.setForeground(Qt.black)
            
            print(f"更新列表项: {file_name} -> {annotation}")  # 调试信息
            current_item.setText(f"{file_name} [{annotation}]")

    def save_annotations(self):
        if not self.annotations:
            print("没有标注数据可保存")  # 调试信息
            return

        print(f"当前标注数据: {self.annotations}")  # 调试信息
        default_filename = f"{self.patient_name}.json"
        save_path, _ = QFileDialog.getSaveFileName(
            self, 
            '保存标注结果', 
            default_filename,
            'JSON files (*.json)'
        )
        
        if save_path:
            print(f"保存到: {save_path}")  # 调试信息
            try:
                with open(save_path, 'w', encoding='utf-8') as f:
                    json.dump(self.annotations, f, ensure_ascii=False, indent=2)
                print("保存成功")  # 调试信息
                # 保存成功后显示状态
                self.status_label.setText('标注状态：保存成功')
                self.status_label.setStyleSheet("QLabel { font-size: 14pt; color: green; }")
            except Exception as e:
                print(f"保存失败: {str(e)}")  # 调试信息
                # 保存失败后显示状态
                self.status_label.setText('标注状态：保存失败')
                self.status_label.setStyleSheet("QLabel { font-size: 14pt; color: red; }")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ImageAnnotator()
    window.show()
    sys.exit(app.exec_())











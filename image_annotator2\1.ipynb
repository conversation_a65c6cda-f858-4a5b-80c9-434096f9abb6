import os
import sys
import pandas as pd
import numpy as np

len(os.listdir('./redraw_pic')),len(os.listdir('contour_mask'))

import shutil

pic_dir = './redraw_pic'
mask_dir = './contour_mask'
pic_files = sorted(os.listdir(pic_dir))
mask_files = sorted(os.listdir(mask_dir))

num_splits = 6
total = len(pic_files)
split_size = total // num_splits

for i in range(num_splits):
    pic_out = f'./split_{i+1}/pic'
    mask_out = f'./split_{i+1}/mask'
    os.makedirs(pic_out, exist_ok=True)
    os.makedirs(mask_out, exist_ok=True)
    start = i * split_size
    end = (i+1) * split_size if i < num_splits - 1 else total
    for pf in pic_files[start:end]:
        shutil.copy(os.path.join(pic_dir, pf), os.path.join(pic_out, pf))
    for mf in mask_files[start:end]:
        shutil.copy(os.path.join(mask_dir, mf), os.path.join(mask_out, mf))

